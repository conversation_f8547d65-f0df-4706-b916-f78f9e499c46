/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyReviewResponseDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：内部制度智能审查响应DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * 内部制度智能审查响应DTO
 * <p>
 * 用于返回内部制度审查的详细结果，
 * 包含风险点、条款问题、关联方风险、合规性评估等信息。
 *
 * 主要功能：
 * 1. 整体风险评估结果
 * 2. 具体风险点列表
 * 3. 条款问题分析
 * 4. 关联方风险评估
 * 5. 改进建议汇总
 */
@Schema(description = "内部制度智能审查响应DTO")
public class InternalPolicyReviewResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 审查记录ID */
    @Schema(description = "审查记录ID")
    private Long reviewId;

    /** 整体风险等级 */
    @Schema(description = "整体风险等级")
    private OverallRiskLevel overallRiskLevel;

    /** 风险分数 (0-100) */
    @Schema(description = "风险分数 (0-100)")
    private Integer riskScore;

    /** 风险总结 */
    @Schema(description = "风险总结")
    private String riskSummary;

    /** 具体风险点列表 */
    @Schema(description = "具体风险点列表")
    private List<PolicyRiskPointDTO> riskPoints;

    /** 条款问题分析 */
    @Schema(description = "条款问题分析")
    private List<PolicyClauseIssueDTO> clauseIssues;

    /** 关联方风险分析 */
    @Schema(description = "关联方风险分析")
    private List<PolicyRelatedPartyRiskDTO> relatedPartyRisks;

    /** 合规性检查结果 */
    @Schema(description = "合规性检查结果")
    private PolicyComplianceAssessmentDTO complianceAssessment;

    /** 审查时间 */
    @Schema(description = "审查时间")
    private Instant reviewTime;

    /** 审查耗时（毫秒） */
    @Schema(description = "审查耗时（毫秒）")
    private Long reviewDuration;

    /** 审查状态 */
    @Schema(description = "审查状态")
    private String reviewStatus;

    /** AI模型信息 */
    @Schema(description = "AI模型信息")
    private String aiModelInfo;

    /** 置信度 (0-100) */
    @Schema(description = "置信度 (0-100)")
    private Integer confidence;

    /** 总体建议措施 */
    @Schema(description = "总体建议措施")
    private List<String> overallRecommendations;

    /** 优先处理事项 */
    @Schema(description = "优先处理事项")
    private List<String> priorityActions;

    /** 后续监控建议 */
    @Schema(description = "后续监控建议")
    private List<String> monitoringSuggestions;

    /** 参考法规文件 */
    @Schema(description = "参考法规文件")
    private List<String> referenceLaws;

    /** 审查报告摘要 */
    @Schema(description = "审查报告摘要")
    private String executiveSummary;

    /** 数据来源说明 */
    @Schema(description = "数据来源说明")
    private List<String> dataSources;

    // 枚举：整体风险等级
    public enum OverallRiskLevel {
        LOW,      // 低风险
        MEDIUM,   // 中等风险
        HIGH,     // 高风险
        CRITICAL  // 严重风险
    }

    public InternalPolicyReviewResponseDTO() {}

    public InternalPolicyReviewResponseDTO(Long reviewId, OverallRiskLevel overallRiskLevel, Integer riskScore, String riskSummary) {
        this.reviewId = reviewId;
        this.overallRiskLevel = overallRiskLevel;
        this.riskScore = riskScore;
        this.riskSummary = riskSummary;
    }

    // Getters and Setters
    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public OverallRiskLevel getOverallRiskLevel() {
        return overallRiskLevel;
    }

    public void setOverallRiskLevel(OverallRiskLevel overallRiskLevel) {
        this.overallRiskLevel = overallRiskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public String getRiskSummary() {
        return riskSummary;
    }

    public void setRiskSummary(String riskSummary) {
        this.riskSummary = riskSummary;
    }

    public List<PolicyRiskPointDTO> getRiskPoints() {
        return riskPoints;
    }

    public void setRiskPoints(List<PolicyRiskPointDTO> riskPoints) {
        this.riskPoints = riskPoints;
    }

    public List<PolicyClauseIssueDTO> getClauseIssues() {
        return clauseIssues;
    }

    public void setClauseIssues(List<PolicyClauseIssueDTO> clauseIssues) {
        this.clauseIssues = clauseIssues;
    }

    public List<PolicyRelatedPartyRiskDTO> getRelatedPartyRisks() {
        return relatedPartyRisks;
    }

    public void setRelatedPartyRisks(List<PolicyRelatedPartyRiskDTO> relatedPartyRisks) {
        this.relatedPartyRisks = relatedPartyRisks;
    }

    public PolicyComplianceAssessmentDTO getComplianceAssessment() {
        return complianceAssessment;
    }

    public void setComplianceAssessment(PolicyComplianceAssessmentDTO complianceAssessment) {
        this.complianceAssessment = complianceAssessment;
    }

    public Instant getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Instant reviewTime) {
        this.reviewTime = reviewTime;
    }

    public Long getReviewDuration() {
        return reviewDuration;
    }

    public void setReviewDuration(Long reviewDuration) {
        this.reviewDuration = reviewDuration;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getAiModelInfo() {
        return aiModelInfo;
    }

    public void setAiModelInfo(String aiModelInfo) {
        this.aiModelInfo = aiModelInfo;
    }

    public Integer getConfidence() {
        return confidence;
    }

    public void setConfidence(Integer confidence) {
        this.confidence = confidence;
    }

    public List<String> getOverallRecommendations() {
        return overallRecommendations;
    }

    public void setOverallRecommendations(List<String> overallRecommendations) {
        this.overallRecommendations = overallRecommendations;
    }

    public List<String> getPriorityActions() {
        return priorityActions;
    }

    public void setPriorityActions(List<String> priorityActions) {
        this.priorityActions = priorityActions;
    }

    public List<String> getMonitoringSuggestions() {
        return monitoringSuggestions;
    }

    public void setMonitoringSuggestions(List<String> monitoringSuggestions) {
        this.monitoringSuggestions = monitoringSuggestions;
    }

    public List<String> getReferenceLaws() {
        return referenceLaws;
    }

    public void setReferenceLaws(List<String> referenceLaws) {
        this.referenceLaws = referenceLaws;
    }

    public String getExecutiveSummary() {
        return executiveSummary;
    }

    public void setExecutiveSummary(String executiveSummary) {
        this.executiveSummary = executiveSummary;
    }

    public List<String> getDataSources() {
        return dataSources;
    }

    public void setDataSources(List<String> dataSources) {
        this.dataSources = dataSources;
    }

    @Override
    public String toString() {
        return "InternalPolicyReviewResponseDTO{" +
            "reviewId=" + reviewId +
            ", overallRiskLevel=" + overallRiskLevel +
            ", riskScore=" + riskScore +
            ", reviewTime=" + reviewTime +
            ", reviewDuration=" + reviewDuration +
            ", confidence=" + confidence +
            '}';
    }
}
