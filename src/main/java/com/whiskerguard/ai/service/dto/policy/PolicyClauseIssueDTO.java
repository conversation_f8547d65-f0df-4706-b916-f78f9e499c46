/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyClauseIssueDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：制度条款问题DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;

/**
 * 制度条款问题DTO
 * <p>
 * 用于描述内部制度中具体条款存在的问题，
 * 包含问题类型、严重程度、修改建议等详细信息。
 *
 * 主要功能：
 * 1. 条款问题详细描述
 * 2. 问题严重程度评估
 * 3. 具体修改建议
 * 4. 法规依据说明
 */
@Schema(description = "制度条款问题DTO")
public class PolicyClauseIssueDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 问题ID */
    @Schema(description = "问题ID")
    private String issueId;

    /** 条款编号 */
    @Schema(description = "条款编号")
    private String clauseNumber;

    /** 条款标题 */
    @Schema(description = "条款标题")
    private String clauseTitle;

    /** 原始条款内容 */
    @Schema(description = "原始条款内容")
    private String originalClause;

    /** 问题类型 */
    @Schema(description = "问题类型")
    private IssueType issueType;

    /** 问题严重程度 */
    @Schema(description = "问题严重程度")
    private SeverityLevel severity;

    /** 问题描述 */
    @Schema(description = "问题详细描述")
    private String issueDescription;

    /** 违反的法律法规 */
    @Schema(description = "违反的法律法规")
    private List<String> violatedLaws;

    /** 潜在风险 */
    @Schema(description = "潜在风险")
    private String potentialRisk;

    /** 修改建议 */
    @Schema(description = "具体修改建议")
    private String modificationSuggestion;

    /** 建议的新条款内容 */
    @Schema(description = "建议的新条款内容")
    private String suggestedClause;

    /** 法规依据 */
    @Schema(description = "法规依据")
    private List<String> legalBasis;

    /** 参考标准 */
    @Schema(description = "参考标准")
    private List<String> referenceStandards;

    /** 影响范围 */
    @Schema(description = "影响范围")
    private String impactScope;

    /** 修改优先级 */
    @Schema(description = "修改优先级")
    private Priority priority;

    /** 相关部门 */
    @Schema(description = "相关部门")
    private List<String> relatedDepartments;

    // 枚举：问题类型
    public enum IssueType {
        LEGAL_VIOLATION,        // 法律违规
        REGULATORY_NON_COMPLIANCE, // 监管不合规
        INTERNAL_CONFLICT,      // 内部冲突
        AMBIGUOUS_WORDING,      // 表述模糊
        MISSING_PROVISION,      // 条款缺失
        OUTDATED_CONTENT,       // 内容过时
        EXCESSIVE_AUTHORITY,    // 权限过大
        INSUFFICIENT_CONTROL,   // 控制不足
        PROCESS_FLAW,          // 流程缺陷
        OTHER                  // 其他问题
    }

    // 枚举：严重程度
    public enum SeverityLevel {
        LOW,      // 轻微问题
        MEDIUM,   // 一般问题
        HIGH,     // 严重问题
        CRITICAL  // 严重问题
    }

    // 枚举：修改优先级
    public enum Priority {
        LOW,       // 低优先级
        MEDIUM,    // 中等优先级
        HIGH,      // 高优先级
        URGENT     // 紧急优先级
    }

    public PolicyClauseIssueDTO() {}

    public PolicyClauseIssueDTO(String issueId, String clauseNumber, IssueType issueType, SeverityLevel severity, String issueDescription) {
        this.issueId = issueId;
        this.clauseNumber = clauseNumber;
        this.issueType = issueType;
        this.severity = severity;
        this.issueDescription = issueDescription;
    }

    // Getters and Setters
    public String getIssueId() {
        return issueId;
    }

    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    public String getClauseNumber() {
        return clauseNumber;
    }

    public void setClauseNumber(String clauseNumber) {
        this.clauseNumber = clauseNumber;
    }

    public String getClauseTitle() {
        return clauseTitle;
    }

    public void setClauseTitle(String clauseTitle) {
        this.clauseTitle = clauseTitle;
    }

    public String getOriginalClause() {
        return originalClause;
    }

    public void setOriginalClause(String originalClause) {
        this.originalClause = originalClause;
    }

    public IssueType getIssueType() {
        return issueType;
    }

    public void setIssueType(IssueType issueType) {
        this.issueType = issueType;
    }

    public SeverityLevel getSeverity() {
        return severity;
    }

    public void setSeverity(SeverityLevel severity) {
        this.severity = severity;
    }

    public String getIssueDescription() {
        return issueDescription;
    }

    public void setIssueDescription(String issueDescription) {
        this.issueDescription = issueDescription;
    }

    public List<String> getViolatedLaws() {
        return violatedLaws;
    }

    public void setViolatedLaws(List<String> violatedLaws) {
        this.violatedLaws = violatedLaws;
    }

    public String getPotentialRisk() {
        return potentialRisk;
    }

    public void setPotentialRisk(String potentialRisk) {
        this.potentialRisk = potentialRisk;
    }

    public String getModificationSuggestion() {
        return modificationSuggestion;
    }

    public void setModificationSuggestion(String modificationSuggestion) {
        this.modificationSuggestion = modificationSuggestion;
    }

    public String getSuggestedClause() {
        return suggestedClause;
    }

    public void setSuggestedClause(String suggestedClause) {
        this.suggestedClause = suggestedClause;
    }

    public List<String> getLegalBasis() {
        return legalBasis;
    }

    public void setLegalBasis(List<String> legalBasis) {
        this.legalBasis = legalBasis;
    }

    public List<String> getReferenceStandards() {
        return referenceStandards;
    }

    public void setReferenceStandards(List<String> referenceStandards) {
        this.referenceStandards = referenceStandards;
    }

    public String getImpactScope() {
        return impactScope;
    }

    public void setImpactScope(String impactScope) {
        this.impactScope = impactScope;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public List<String> getRelatedDepartments() {
        return relatedDepartments;
    }

    public void setRelatedDepartments(List<String> relatedDepartments) {
        this.relatedDepartments = relatedDepartments;
    }

    @Override
    public String toString() {
        return "PolicyClauseIssueDTO{" +
            "issueId='" + issueId + '\'' +
            ", clauseNumber='" + clauseNumber + '\'' +
            ", clauseTitle='" + clauseTitle + '\'' +
            ", issueType=" + issueType +
            ", severity=" + severity +
            ", priority=" + priority +
            '}';
    }
}
