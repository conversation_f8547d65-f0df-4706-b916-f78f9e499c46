/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyRelatedPartyRiskDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：制度关联方风险DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 制度关联方风险DTO
 * <p>
 * 用于描述内部制度中涉及的关联方（母公司、子公司、部门等）
 * 可能存在的风险，包含关联方信息、风险评估、背景调查结果等。
 *
 * 主要功能：
 * 1. 关联方基本信息
 * 2. 风险等级评估
 * 3. 背景调查结果
 * 4. 风险缓解建议
 */
@Schema(description = "制度关联方风险DTO")
public class PolicyRelatedPartyRiskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 关联方ID */
    @Schema(description = "关联方ID")
    private String partyId;

    /** 关联方名称 */
    @Schema(description = "关联方名称")
    private String partyName;

    /** 关联方类型 */
    @Schema(description = "关联方类型")
    private PartyType partyType;

    /** 关联关系 */
    @Schema(description = "关联关系")
    private String relationship;

    /** 风险等级 */
    @Schema(description = "风险等级")
    private RiskLevel riskLevel;

    /** 风险分数 (0-100) */
    @Schema(description = "风险分数 (0-100)")
    private Integer riskScore;

    /** 风险描述 */
    @Schema(description = "风险详细描述")
    private String riskDescription;

    /** 识别的风险点 */
    @Schema(description = "识别的风险点")
    private List<String> identifiedRisks;

    /** 企业基本信息 */
    @Schema(description = "企业基本信息")
    private Map<String, Object> basicInfo;

    /** 经营状态 */
    @Schema(description = "经营状态")
    private String businessStatus;

    /** 信用评级 */
    @Schema(description = "信用评级")
    private String creditRating;

    /** 法律纠纷记录 */
    @Schema(description = "法律纠纷记录")
    private List<String> legalDisputes;

    /** 行政处罚记录 */
    @Schema(description = "行政处罚记录")
    private List<String> administrativePenalties;

    /** 财务风险指标 */
    @Schema(description = "财务风险指标")
    private Map<String, Object> financialRiskIndicators;

    /** 合规风险评估 */
    @Schema(description = "合规风险评估")
    private String complianceRiskAssessment;

    /** 风险缓解建议 */
    @Schema(description = "风险缓解建议")
    private List<String> riskMitigationSuggestions;

    /** 监控建议 */
    @Schema(description = "监控建议")
    private List<String> monitoringSuggestions;

    /** 数据来源 */
    @Schema(description = "数据来源")
    private List<String> dataSources;

    /** 最后更新时间 */
    @Schema(description = "最后更新时间")
    private String lastUpdated;

    // 枚举：关联方类型
    public enum PartyType {
        PARENT_COMPANY,     // 母公司
        SUBSIDIARY,         // 子公司
        AFFILIATE,          // 关联公司
        DEPARTMENT,         // 部门
        BRANCH,            // 分支机构
        JOINT_VENTURE,     // 合资企业
        SUPPLIER,          // 供应商
        CUSTOMER,          // 客户
        PARTNER,           // 合作伙伴
        OTHER              // 其他
    }

    // 枚举：风险等级
    public enum RiskLevel {
        LOW,      // 低风险
        MEDIUM,   // 中等风险
        HIGH,     // 高风险
        CRITICAL  // 严重风险
    }

    public PolicyRelatedPartyRiskDTO() {}

    public PolicyRelatedPartyRiskDTO(String partyId, String partyName, PartyType partyType, RiskLevel riskLevel) {
        this.partyId = partyId;
        this.partyName = partyName;
        this.partyType = partyType;
        this.riskLevel = riskLevel;
    }

    // Getters and Setters
    public String getPartyId() {
        return partyId;
    }

    public void setPartyId(String partyId) {
        this.partyId = partyId;
    }

    public String getPartyName() {
        return partyName;
    }

    public void setPartyName(String partyName) {
        this.partyName = partyName;
    }

    public PartyType getPartyType() {
        return partyType;
    }

    public void setPartyType(PartyType partyType) {
        this.partyType = partyType;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public String getRiskDescription() {
        return riskDescription;
    }

    public void setRiskDescription(String riskDescription) {
        this.riskDescription = riskDescription;
    }

    public List<String> getIdentifiedRisks() {
        return identifiedRisks;
    }

    public void setIdentifiedRisks(List<String> identifiedRisks) {
        this.identifiedRisks = identifiedRisks;
    }

    public Map<String, Object> getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(Map<String, Object> basicInfo) {
        this.basicInfo = basicInfo;
    }

    public String getBusinessStatus() {
        return businessStatus;
    }

    public void setBusinessStatus(String businessStatus) {
        this.businessStatus = businessStatus;
    }

    public String getCreditRating() {
        return creditRating;
    }

    public void setCreditRating(String creditRating) {
        this.creditRating = creditRating;
    }

    public List<String> getLegalDisputes() {
        return legalDisputes;
    }

    public void setLegalDisputes(List<String> legalDisputes) {
        this.legalDisputes = legalDisputes;
    }

    public List<String> getAdministrativePenalties() {
        return administrativePenalties;
    }

    public void setAdministrativePenalties(List<String> administrativePenalties) {
        this.administrativePenalties = administrativePenalties;
    }

    public Map<String, Object> getFinancialRiskIndicators() {
        return financialRiskIndicators;
    }

    public void setFinancialRiskIndicators(Map<String, Object> financialRiskIndicators) {
        this.financialRiskIndicators = financialRiskIndicators;
    }

    public String getComplianceRiskAssessment() {
        return complianceRiskAssessment;
    }

    public void setComplianceRiskAssessment(String complianceRiskAssessment) {
        this.complianceRiskAssessment = complianceRiskAssessment;
    }

    public List<String> getRiskMitigationSuggestions() {
        return riskMitigationSuggestions;
    }

    public void setRiskMitigationSuggestions(List<String> riskMitigationSuggestions) {
        this.riskMitigationSuggestions = riskMitigationSuggestions;
    }

    public List<String> getMonitoringSuggestions() {
        return monitoringSuggestions;
    }

    public void setMonitoringSuggestions(List<String> monitoringSuggestions) {
        this.monitoringSuggestions = monitoringSuggestions;
    }

    public List<String> getDataSources() {
        return dataSources;
    }

    public void setDataSources(List<String> dataSources) {
        this.dataSources = dataSources;
    }

    public String getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(String lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    @Override
    public String toString() {
        return "PolicyRelatedPartyRiskDTO{" +
            "partyId='" + partyId + '\'' +
            ", partyName='" + partyName + '\'' +
            ", partyType=" + partyType +
            ", relationship='" + relationship + '\'' +
            ", riskLevel=" + riskLevel +
            ", riskScore=" + riskScore +
            '}';
    }
}
