/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyRiskPointDTO.java
 * 包    名：com.whiskerguard.ai.service.dto.policy
 * 描    述：制度风险点DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;

/**
 * 制度风险点DTO
 * <p>
 * 用于描述内部制度中识别出的具体风险点，
 * 包含风险类型、严重程度、影响范围等详细信息。
 *
 * 主要功能：
 * 1. 风险点详细描述
 * 2. 风险等级评估
 * 3. 影响范围分析
 * 4. 改进建议提供
 */
@Schema(description = "制度风险点DTO")
public class PolicyRiskPointDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 风险点ID */
    @Schema(description = "风险点ID")
    private String riskId;

    /** 风险类型 */
    @Schema(description = "风险类型")
    private RiskType riskType;

    /** 风险等级 */
    @Schema(description = "风险等级")
    private RiskLevel riskLevel;

    /** 风险标题 */
    @Schema(description = "风险标题")
    private String title;

    /** 风险描述 */
    @Schema(description = "风险详细描述")
    private String description;

    /** 涉及的制度条款 */
    @Schema(description = "涉及的制度条款")
    private List<String> affectedClauses;

    /** 潜在影响 */
    @Schema(description = "潜在影响")
    private String potentialImpact;

    /** 违反的法律法规 */
    @Schema(description = "违反的法律法规")
    private List<String> violatedRegulations;

    /** 改进建议 */
    @Schema(description = "改进建议")
    private List<String> recommendations;

    /** 风险分数 (0-100) */
    @Schema(description = "风险分数 (0-100)")
    private Integer riskScore;

    /** 紧急程度 */
    @Schema(description = "紧急程度")
    private UrgencyLevel urgency;

    /** 相关部门 */
    @Schema(description = "相关部门")
    private List<String> relatedDepartments;

    /** 参考案例 */
    @Schema(description = "参考案例")
    private String referenceCase;

    // 枚举：风险类型
    public enum RiskType {
        LEGAL_COMPLIANCE,      // 法律合规风险
        OPERATIONAL,           // 操作风险
        FINANCIAL,            // 财务风险
        REGULATORY,           // 监管风险
        REPUTATIONAL,         // 声誉风险
        INTERNAL_CONTROL,     // 内控风险
        CONFLICT_OF_INTEREST, // 利益冲突风险
        DATA_PRIVACY,         // 数据隐私风险
        OTHER                 // 其他风险
    }

    // 枚举：风险等级
    public enum RiskLevel {
        LOW,      // 低风险
        MEDIUM,   // 中等风险
        HIGH,     // 高风险
        CRITICAL  // 严重风险
    }

    // 枚举：紧急程度
    public enum UrgencyLevel {
        LOW,       // 低紧急度
        MEDIUM,    // 中等紧急度
        HIGH,      // 高紧急度
        IMMEDIATE  // 立即处理
    }

    public PolicyRiskPointDTO() {}

    public PolicyRiskPointDTO(String riskId, RiskType riskType, RiskLevel riskLevel, String title, String description) {
        this.riskId = riskId;
        this.riskType = riskType;
        this.riskLevel = riskLevel;
        this.title = title;
        this.description = description;
    }

    // Getters and Setters
    public String getRiskId() {
        return riskId;
    }

    public void setRiskId(String riskId) {
        this.riskId = riskId;
    }

    public RiskType getRiskType() {
        return riskType;
    }

    public void setRiskType(RiskType riskType) {
        this.riskType = riskType;
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getAffectedClauses() {
        return affectedClauses;
    }

    public void setAffectedClauses(List<String> affectedClauses) {
        this.affectedClauses = affectedClauses;
    }

    public String getPotentialImpact() {
        return potentialImpact;
    }

    public void setPotentialImpact(String potentialImpact) {
        this.potentialImpact = potentialImpact;
    }

    public List<String> getViolatedRegulations() {
        return violatedRegulations;
    }

    public void setViolatedRegulations(List<String> violatedRegulations) {
        this.violatedRegulations = violatedRegulations;
    }

    public List<String> getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(List<String> recommendations) {
        this.recommendations = recommendations;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public UrgencyLevel getUrgency() {
        return urgency;
    }

    public void setUrgency(UrgencyLevel urgency) {
        this.urgency = urgency;
    }

    public List<String> getRelatedDepartments() {
        return relatedDepartments;
    }

    public void setRelatedDepartments(List<String> relatedDepartments) {
        this.relatedDepartments = relatedDepartments;
    }

    public String getReferenceCase() {
        return referenceCase;
    }

    public void setReferenceCase(String referenceCase) {
        this.referenceCase = referenceCase;
    }

    @Override
    public String toString() {
        return "PolicyRiskPointDTO{" +
            "riskId='" + riskId + '\'' +
            ", riskType=" + riskType +
            ", riskLevel=" + riskLevel +
            ", title='" + title + '\'' +
            ", description='" + description + '\'' +
            ", riskScore=" + riskScore +
            ", urgency=" + urgency +
            '}';
    }
}
